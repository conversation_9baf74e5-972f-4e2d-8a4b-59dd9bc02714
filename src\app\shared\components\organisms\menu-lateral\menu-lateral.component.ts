import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    inject,
    signal
} from '@angular/core';
import { Router, RouterModule } from '@angular/router';

// PrimeNG Components
import { AvatarModule } from 'primeng/avatar';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { RippleModule } from 'primeng/ripple';

// Models
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'app-menu-lateral',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MenuModule,
    ButtonModule,
    AvatarModule,
    BadgeModule,
    RippleModule
  ],
  templateUrl: './menu-lateral.component.html',
  styleUrl: './menu-lateral.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuLateralComponent {
  private router = inject(Router);

  // Estado do menu
  collapsed = signal<boolean>(false);

  // Itens do menu
  menuItems = signal<MenuItem[]>([
    {
      label: 'Operações',
      icon: 'pi pi-cog',
      expanded: true,
      items: [
        {
          label: 'Containers',
          icon: 'pi pi-box',
          routerLink: '/containers',
          command: () => this.navigateTo('/containers')
        },
        {
          label: 'Pedidos',
          icon: 'pi pi-file-edit',
          routerLink: '/pedidos',
          command: () => this.navigateTo('/pedidos')
        }
      ]
    },
    {
      label: 'Clientes',
      icon: 'pi pi-users',
      routerLink: '/clientes',
      command: () => this.navigateTo('/clientes')
    }
  ]);

  // Item ativo
  activeItem = signal<string>('/containers');

  constructor() {
    // Define o item ativo baseado na rota atual
    this.setActiveItem(this.router.url);
  }

  /**
   * Toggle do estado collapsed do menu
   */
  toggleCollapsed(): void {
    this.collapsed.set(!this.collapsed());
  }

  /**
   * Navega para uma rota específica
   */
  private navigateTo(route: string): void {
    this.router.navigate([route]);
    this.setActiveItem(route);
  }

  /**
   * Define o item ativo
   */
  private setActiveItem(route: string): void {
    this.activeItem.set(route);
  }

  /**
   * Verifica se um item está ativo
   */
  isActive(route: string): boolean {
    return this.activeItem() === route;
  }


}
