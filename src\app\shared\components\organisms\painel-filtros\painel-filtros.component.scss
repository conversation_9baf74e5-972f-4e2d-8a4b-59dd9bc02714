.painel-filtros {
  width: 350px;
  min-width: 350px;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #dee2e6;

  ::ng-deep {
    .p-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      border: none;
      box-shadow: none;
      background-color: white;

      .p-card-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
        background-color: #ffffff;
        color: white;
      }

      .p-card-content {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        max-height: calc(100vh - 200px);
        background-color: white;
      }

      .p-card-footer {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
      }
    }
  }
}

.filtros-content {
  .filtro-section {
    .filtro-title {
      color: #495057;
      font-size: 0.9rem;
      font-weight: 600;
      // margin-bottom: 1rem;
      display: flex;
      align-items: center;

      i {
        color: #6c757d;
        margin-right: 0.5rem;
      }
    }
    .field {
      margin-bottom: 0.5rem;

      label {
        color: #6c757d;
        font-size: 0.8rem;
        font-weight: 500;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.equipment-icon {
  border-radius: 4px;
  object-fit: contain;
}

// Customizações para componentes PrimeNG
::ng-deep {
  .p-multiselect,
  .p-autocomplete,
  .p-calendar,
  .p-inputtext {
    font-size: 0.875rem;
    &.p-component {
      border-radius: 6px;
      border: 1px solid #ced4da;
      background-color: #f8f9fa; // Fundo cinza claro
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      &:hover {
        border-color: #adb5bd;
        background-color: #e9ecef; // Cinza um pouco mais escuro no hover
      }

      &.p-focus {
        border-color: #86b7fe;
        background-color: white; // Fundo branco quando focado
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }
    }
  }

  .p-multiselect-panel,
  .p-autocomplete-panel {
    border-radius: 6px;
    box-shadow: 0 4px 12px #f8f9fa;
    border: 1px solid #dee2e6;
  }

  .p-multiselect-item,
  .p-autocomplete-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;

    &:hover {
      background-color: #f8f9fa;
    }

    &.p-highlight {
      background-color: #e7f3ff;
      color: #0d6efd;
    }
  }

  .p-button {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;

    &.p-button-outlined {
      background-color: transparent;
    }
  }

  .p-divider {
    margin: 0.5rem 0;

    &.p-divider-horizontal {
      &:before {
        border-top: 1px solid #e9ecef;
      }
    }
  }

  .p-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  // Scrollbar customizada
  .p-card-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .painel-filtros {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 50vh;

    ::ng-deep {
      .p-card-content {
        max-height: 40vh;
      }
    }
  }
}
