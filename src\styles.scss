/* You can add global styles to this file, and also import other style files */

/* Imports do PrimeNG */
@import "primeicons/primeicons.css";

/* Reset e configurações globais para layout em tela cheia */
html {
  height: 100%;
  width: 100%;
}

body {
  padding: 0;
  margin: 0;
  height: 100vh;
  width: 100vw;
  background-color: #f8f9fa;
}

app-root {
  display: block;
  height: 100vh;
  width: 100vw;
}

* {
  font-family: "Roboto", sans-serif;
  box-sizing: border-box;
  outline: none;
}

/* Cores personalizadas - Paleta Azul e Branco */
:root {
  --primary-color: #007bff;
  --primary-color-text: #ffffff;
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-200: #90caf9;
  --primary-300: #64b5f6;
  --primary-400: #42a5f5;
  --primary-500: #2196f3;
  --primary-600: #1e88e5;
  --primary-700: #1976d2;
  --primary-800: #1565c0;
  --primary-900: #0d47a1;

  --surface-0: #ffffff;
  --surface-50: #f8f9fa;
  --surface-100: #e9ecef;
  --surface-200: #dee2e6;
  --surface-300: #ced4da;
  --surface-400: #adb5bd;
  --surface-500: #6c757d;
  --surface-600: #495057;
  --surface-700: #343a40;
  --surface-800: #212529;
  --surface-900: #000000;

  --text-color: #495057;
  --text-color-secondary: #6c757d;
  --border-color: #dee2e6;

  --blue-50: #e3f2fd;
  --blue-100: #bbdefb;
  --blue-500: #2196f3;
  --blue-600: #1e88e5;
  --blue-700: #1976d2;
}

/* Customizações para PrimeNG */
.p-component {
  font-family: inherit;
}

/* Estilos específicos para o Google Maps */
.gm-style {
  font-family: "Roboto", sans-serif !important;
}

/* Customizações para marcadores do mapa */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;

  &:hover {
    background: #a8a8a8;
  }
}
.map-container {
  width: 100% !important;
  height: 100% !important;
}

.p-multiselect-overlay, .p-autocomplete-overlay {
  background-color: #f8f9fada !important;
  background: #f8f9fa !important;
  color:#000000 !important;
}

.p-autocomplete-input-multiple {
  border-color: #ced4da !important;
  background-color: #f8f9fada !important;
}

.p-multiselect-option {
  color:#000000 !important;
}

.p-drawer, .p-autocomplete-input-chip {
  background: #f3f3f3 !important;
  color: #453e3e !important;
}

.p-multiselect-label {
  color: #000000 !important;
}

.p-inputtext {
  color: #000000 !important;
}

.p-focus {
  background: #a1a1a3 !important;
  color:#000000 !important;
}
.p-autocomplete-option {
  color: #000000 !important;
}
.p-checkbox-box {
  background-color: #f2f2f2 !important;
}

/* Utilitários */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Utilitários adicionais para o novo layout */
.text-muted {
  color: var(--text-color-secondary) !important;
}

.border-bottom-1 {
  border-bottom: 1px solid var(--border-color) !important;
}

.surface-border {
  border-color: var(--border-color) !important;
}

/* Classes utilitárias para paleta azul e branco */
.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-primary-50 {
  background-color: var(--primary-50) !important;
}

.bg-primary-100 {
  background-color: var(--primary-100) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-primary-600 {
  color: var(--primary-600) !important;
}

.text-primary-700 {
  color: var(--primary-700) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

.border-primary-200 {
  border-color: var(--primary-200) !important;
}

/* Customizações específicas para PrimeNG com paleta azul */
::ng-deep {
  .p-button.p-button-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover {
      background-color: var(--primary-600);
      border-color: var(--primary-600);
    }

    &:active {
      background-color: var(--primary-700);
      border-color: var(--primary-700);
    }
  }

  .p-button.p-button-outlined.p-button-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover {
      background-color: var(--primary-50);
      color: var(--primary-600);
      border-color: var(--primary-600);
    }
  }

  .p-sidebar-header {
    background-color: var(--primary-50);
    border-bottom-color: var(--primary-200);
  }

  .p-menu .p-menuitem-link:hover {
    background-color: var(--primary-50);
  }

  .p-menu .p-menuitem-link.p-menuitem-link-active {
    background-color: var(--primary-100);
    color: var(--primary-700);
  }
}
