<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.br.sasw.portalcacamba.repository.ecovisao.FilialRepository">

  <select id="findAll" parameterType="com.br.sasw.portalcacamba.entity.dto.request.FilialFiltro" resultType="com.br.sasw.portalcacamba.entity.dto.response.FilialResponse">
      SELECT FORMAT(f.CodFil, '0000') as codigoFormatado,
      f.CodFil  as codigo,
      f.descricao as nome,
      f.endereco,
      f.bairro,
      f.cidade,
      f.uf from filiais f WHERE 0 = 0

      <if test="codigo != null">
        AND f.CodFil = #{codigo}
      </if>

      <if test="nome != null and nome != ''">
          AND f.descricao LIKE CONCAT('%', #{nome}, '%')
      </if>
  </select>
</mapper>
