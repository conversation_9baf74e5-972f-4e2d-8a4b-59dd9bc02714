import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { EquipamentoService, FilialInfo } from '@core/services/equipamento.service';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-info-usuario-filial',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="info-container">
      @if (filialInfo()) {
        <div class="info-content">
          <div class="filial-info">
            <span class="label">Filial:</span>
            <span class="value">{{ filialInfo()?.codigoFormatado }} - {{ filialInfo()?.nome }}</span>
          </div>
          <div class="endereco-info">
            <span class="label">Endereço:</span>
            <span class="value">{{ filialInfo()?.endereco }}, {{ filialInfo()?.bairro }} - {{ filialInfo()?.cidade }}/{{ filialInfo()?.uf }}</span>
          </div>
          <div class="usuario-info">
            <span class="label">Usuário:</span>
            <span class="value">Sistema Portal</span>
          </div>
        </div>
      } @else if (loading()) {
        <div class="loading-info">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Carregando informações...</span>
        </div>
      } @else if (error()) {
        <div class="error-info">
          <i class="pi pi-exclamation-triangle"></i>
          <span>Erro ao carregar informações</span>
        </div>
      }
    </div>
  `,
  styleUrls: ['./info-usuario-filial.component.scss']
})
export class InfoUsuarioFilialComponent implements OnInit {
  private equipamentoService = inject(EquipamentoService);

  // Signals
  filialInfo = signal<FilialInfo | null>(null);
  loading = signal<boolean>(false);
  error = signal<boolean>(false);

  ngOnInit(): void {
    this.loadFilialInfo();
  }

  private loadFilialInfo(): void {
    this.loading.set(true);
    this.error.set(false);

    this.equipamentoService.getFilialInfo()
      .pipe(
        map((lista: any) => lista[0])
      )
      .subscribe({
        next: (info) => {
          this.filialInfo.set(info);
          this.loading.set(false);
        },
        error: (err) => {
          console.error('Erro ao carregar informações da filial:', err);
          this.error.set(true);
          this.loading.set(false);
        }
      });
  }
}
