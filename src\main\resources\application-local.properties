server.port=8082
spring.application.name=api-portalcacambas-springboot
server.servlet.context-path=/api

logging.level.org.springframework.ws.client.MessageTracing=DEBUG
logging.level.org.apache.http.wire=DEBUG
javax.net.debug=ssl

#ECOVISAO DB
spring.datasource.jdbc-url=******************************************************************************************************
spring.datasource.username=saswecovisao
spring.datasource.password=ecovisao@2018$
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

#SATELLITE DB
bd2.datasource.jdbc-url=****************************************************************************************************
bd2.datasource.username=sasw
bd2.datasource.password=s@$26bd1
bd2.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

logging.level.org.springframework.jdbc=DEBUG
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl