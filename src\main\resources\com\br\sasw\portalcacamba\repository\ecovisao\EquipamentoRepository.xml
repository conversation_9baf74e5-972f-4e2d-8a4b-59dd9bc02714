<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.br.sasw.portalcacamba.repository.ecovisao.EquipamentoRepository">

  <select id="findAll" parameterType="com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro" resultType="com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse">
      SELECT
      e.TipoEquip as tipoEquipamento,
      c.nred,
      c.latitude,
      c.longitude,
      c.Ende as endereco,
      c.<PERSON> as bairro,
      c.Cidade as cidade,
      c.<PERSON>stado as estado,
      c.CEP as cep,
      Funcion.Nome as nome,
      DATEDIFF(day, co.DtUltMov, convert(date, Getdate())) as tempoDias,
      convert(date, co.DtUltMov) as dataUltMovimento,
      co.IDEquip as id,
      e.<PERSON> as situacao,
      co.Codfil as codFil,
      Case when Isnull(clifat.Limite, 0) &lt;= 0 then 15 else Isnull(clifat.Limite, 0) end as limite,
      Case when Isnull(c.Limite, 0) &lt;= 0 then 15 else Isnull(c.Limite, 0) end as limiteLocal,
      p.Solicitante as solicitante,
      clifat.NRed as nredFat,
      FORMAT(DATEADD(day, Case when Isnull(clifat.Limite, 0) &lt;= 0 then 10 else Isnull(clifat.Limite, 0) end, convert(date, co.DtUltMov)), 'dd/MM/yyyy') as dataPrevisaoColeta,
      FORMAT(convert(date, co.DtUltMov), 'dd/MM/yyyy') as dataEntrega
      FROM
      equipamentos e
      LEFT JOIN CtrOperequip co on co.IDEquip = e.IDEquip
      LEFT JOIN Clientes c ON c.codigo = co.codcli1 AND c.codfil = co.codfil
      LEFT JOIN Rt_Guias rtg on rtg.Guia = co.Guia and rtg.Serie = co.Serie
      LEFT JOIN Os_Vig ov on ov.OS = rtg.OS and ov.CodFil = co.CodFil
      LEFT JOIN Clientes clifat on clifat.Codigo = ov.CliFat and clifat.CodFil = ov.CodFil
      LEFT JOIN Pedido p on p.SeqRota = co.SeqRota and p.Parada = co.Parada
      LEFT JOIN Escala on Escala.SeqRota = co.SeqRota
      LEFT JOIN Funcion on Funcion.Matr = Escala.MatrMot

      WHERE
      e.Situacao = 'A'
      AND c.Latitude &lt;> ''
      AND c.Longitude &lt;> ''

      <if test="codFil != null">
        AND co.codFil = #{codFil}
      </if>

      <if test="tipoEquip != null and tipoEquip.size > 0">
        AND e.TipoEquip IN
        <foreach item="item" collection="tipoEquip" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="nred != null and nred.size > 0">
        AND c.NRed IN
        <foreach item="item" collection="nred" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="bairro != null and bairro.size() > 0">
        AND c.Bairro IN
        <foreach item="item" collection="bairro" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="nredFat != null and nredFat.size > 0">
        AND clifat.NRed IN
        <foreach item="item" collection="nredFat" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="solicitante != null and solicitante.size > 0">
        AND p.Solicitante IN
        <foreach item="item" collection="solicitante" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </select>

    <!-- 1. Buscar NRED (serviços) -->
    <select id="findAllServices" parameterType="string" resultType="string">
        SELECT DISTINCT c.NRed
        FROM equipamentos e
        LEFT JOIN CtrOperequip co ON co.IDEquip = e.IDEquip
        JOIN Clientes c ON c.CodFil = co.CodFil AND c.Codigo = co.CodCli1
        WHERE e.Situacao = 'A'
        AND c.Latitude &lt;> ''
        AND c.Longitude &lt;> ''
        AND c.NRed &lt;> ''

        <if test="place != null and place != ''">
            AND c.NRed LIKE CONCAT('%', #{place}, '%')
        </if>

        ORDER BY c.NRed ASC
    </select>

    <!-- 2. Buscar NRedFat (clientes faturamento) -->
    <select id="findAllClients" parameterType="string" resultType="string">
        SELECT DISTINCT clifat.NRed
        FROM equipamentos e
        LEFT JOIN CtrOperequip co ON co.IDEquip = e.IDEquip
        JOIN Clientes c ON c.CodFil = co.CodFil AND c.Codigo = co.CodCli1
        LEFT JOIN Rt_Guias rtg ON rtg.Guia = co.Guia AND rtg.Serie = co.Serie
        LEFT JOIN OS_Vig ov ON ov.OS = rtg.OS AND ov.CodFil = co.CodFil
        LEFT JOIN Clientes clifat ON clifat.Codigo = ov.CliFat AND clifat.CodFil = ov.CodFil
        LEFT JOIN Pedido p ON p.SeqRota = co.SeqRota AND p.Parada = co.Parada
        WHERE e.Situacao = 'A'
        AND c.Latitude &lt;> ''
        AND c.Longitude &lt;> ''
        AND clifat.NRed &lt;> ''

        <if test="client != null and client != ''">
            AND clifat.NRed LIKE CONCAT('%', #{client}, '%')
        </if>

        ORDER BY clifat.NRed ASC
    </select>

    <!-- 3. Buscar solicitantes -->
    <select id="findAllRequester" parameterType="string" resultType="string">
        SELECT DISTINCT p.Solicitante
        FROM equipamentos e
        LEFT JOIN CtrOperequip co ON co.IDEquip = e.IDEquip
        JOIN Clientes c ON c.CodFil = co.CodFil AND c.Codigo = co.CodCli1
        LEFT JOIN Rt_Guias rtg ON rtg.Guia = co.Guia AND rtg.Serie = co.Serie
        LEFT JOIN OS_Vig ov ON ov.OS = rtg.OS AND ov.CodFil = co.CodFil
        LEFT JOIN Clientes clifat ON clifat.Codigo = ov.CliFat AND clifat.CodFil = ov.CodFil
        LEFT JOIN Pedido p ON p.SeqRota = co.SeqRota AND p.Parada = co.Parada
        WHERE e.Situacao = 'A'
        AND c.Latitude &lt;> ''
        AND c.Longitude &lt;> ''
        AND p.Solicitante &lt;> ''

        <if test="requester != null and requester != ''">
            AND p.Solicitante LIKE CONCAT('%', #{requester}, '%')
        </if>

        ORDER BY p.Solicitante ASC
    </select>

    <!-- 4. Buscar bairros -->
    <select id="findAllPlaces" parameterType="string" resultType="string">
        SELECT DISTINCT c.Bairro
        FROM equipamentos e
        LEFT JOIN CtrOperequip co ON co.IDEquip = e.IDEquip
        JOIN Clientes c ON c.CodFil = co.CodFil AND c.Codigo = co.CodCli1
        WHERE e.Situacao = 'A'
        AND c.Latitude &lt;> ''
        AND c.Longitude &lt;> ''
        AND c.Bairro &lt;> ''

        <if test="place != null and place != ''">
            AND c.Bairro LIKE CONCAT('%', #{place}, '%')
        </if>

        ORDER BY c.Bairro ASC
    </select>
</mapper>
