package com.br.sasw.portalcacamba.controller;

import com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse;
import com.br.sasw.portalcacamba.service.EquipamentoService;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/equipamentos")
@RequiredArgsConstructor
public class EquipamentoController {

    private final EquipamentoService service;

    @GetMapping
    public List<EquipamentoResponse> findAll(@ParameterObject EquipamentoFiltro filters) {
        return service.findAll(filters);
    }

    @GetMapping("/clientes")
    public List<String> findAllClients(@RequestParam(required = false) String client) {
        return service.findAllClients(client);
    }

    @GetMapping("/servicos")
    public List<String> findAllServices(@RequestParam(required = false) String service) {
        return this.service.findAllServices(service);
    }

    @GetMapping("/solicitantes")
    public List<String> findAllRequester(@RequestParam(required = false) String requester) {
        return service.findAllRequester(requester);
    }

    @GetMapping("/bairros")
    public List<String> findAllPlaces(@RequestParam(required = false) String place) {
        return service.findAllPlaces(place);
    }
}
