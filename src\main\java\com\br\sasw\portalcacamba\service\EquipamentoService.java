package com.br.sasw.portalcacamba.service;

import com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse;
import com.br.sasw.portalcacamba.repository.ecovisao.EquipamentoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class EquipamentoService {

    private final EquipamentoRepository repository;

    public List<EquipamentoResponse> findAll(EquipamentoFiltro filters) {

        log.info("Filtros - {}", filters);
        return repository.findAll(filters);
    }

    public List<String> findAllClients(String client) {
        return repository.findAllClients(client);
    }

    public List<String> findAllRequester(String requester) {
        return repository.findAllRequester(requester);
    }

    public List<String> findAllPlaces(String place) {
        return repository.findAllPlaces(place);
    }

    public List<String> findAllServices(String service) {
        return repository.findAllServices(service);
    }
}
