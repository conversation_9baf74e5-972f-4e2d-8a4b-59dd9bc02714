<div class="painel-filtros h-full">
  <p-card class="h-full">
    <!-- <ng-template pTemplate="header">
      <div class="flex align-items-center justify-content-between p-3">
        <h5 class="m-0 text-primary">
          <i class="pi pi-filter mr-2"></i>
          Filtros
        </h5>
        @if (hasActiveFilters()) {
          <p-badge 
            [value]="'Ativos'" 
            severity="success" 
            size="small">
          </p-badge>
        }
      </div>
    </ng-template> -->

    <ng-template pTemplate="content">
      <div class="filtros-content flex flex-column gap-2">
        


        <!-- Tipos de Equipamento -->
        <div class="filtro-section">
          <h6 class="filtro-title">
            <i class="pi pi-box mr-2"></i>
            Tipos de Equipamento
          </h6>
          
          <div class="field">
            <p-multiSelect
              [options]="tiposEquipamento"
              [ngModel]="filtros().tiposEquipamento"
              (ngModelChange)="onTiposEquipamentoChange($event)"
              placeholder="Selecione os tipos"
              optionLabel="label"
              optionValue="value"
              [showToggleAll]="true"
              [showClear]="true"
              class="w-full">
              
              <ng-template let-option pTemplate="item">
                <div class="flex align-items-center gap-2">
                  <img 
                    [src]="'assets/images/' + option.icon" 
                    [alt]="option.label"
                    class="equipment-icon"
                    width="20" 
                    height="20">
                  <span>{{ option.label }}</span>
                </div>
              </ng-template>
            </p-multiSelect>
          </div>
        </div>

        <p-divider></p-divider>

        <!-- Serviços -->
        <div class="filtro-section">
          <h6 class="filtro-title">
            <i class="pi pi-cog mr-2"></i>
            Serviços
          </h6>
          
          <div class="field">
            <p-autoComplete
              [ngModel]="filtros().servicos"
              (ngModelChange)="onServicosChange($event)"
              [suggestions]="servicosSuggestions()"
              (completeMethod)="searchServicos($event)"
              [multiple]="true"
              placeholder="Digite para buscar serviços"
              [showClear]="true"
              class="w-full">
            </p-autoComplete>
          </div>
        </div>

        <p-divider></p-divider>

        <!-- Clientes -->
        <div class="filtro-section">
          <h6 class="filtro-title">
            <i class="pi pi-users mr-2"></i>
            Clientes
          </h6>
          
          <div class="field">
            <p-autoComplete
              [ngModel]="filtros().clientes"
              (ngModelChange)="onClientesChange($event)"
              [suggestions]="clientesSuggestions()"
              (completeMethod)="searchClientes($event)"
              [multiple]="true"
              placeholder="Digite para buscar clientes"
              [showClear]="true"
              class="w-full">
            </p-autoComplete>
          </div>
        </div>

        <p-divider></p-divider>

        <!-- Bairros -->
        <div class="filtro-section">
          <h6 class="filtro-title">
            <i class="pi pi-map mr-2"></i>
            Bairros
          </h6>
          
          <div class="field">
            <p-autoComplete
              [ngModel]="filtros().bairros"
              (ngModelChange)="onBairrosChange($event)"
              [suggestions]="bairrosSuggestions()"
              (completeMethod)="searchBairros($event)"
              [multiple]="true"
              placeholder="Digite para buscar bairros"
              [showClear]="true"
              appendTo="body"
              class="w-full">
            </p-autoComplete>
          </div>
        </div>

        <p-divider></p-divider>

        <!-- Solicitantes -->
        <div class="filtro-section">
          <h6 class="filtro-title">
            <i class="pi pi-user mr-2"></i>
            Solicitantes
          </h6>
          
          <div class="field">
            <p-autoComplete
              [ngModel]="filtros().solicitantes"
              (ngModelChange)="onSolicitantesChange($event)"
              [suggestions]="solicitantesSuggestions()"
              (completeMethod)="searchSolicitantes($event)"
              [multiple]="true"
              placeholder="Digite para buscar solicitantes"
              [showClear]="true"
              appendTo="body"
              class="w-full">
            </p-autoComplete>
          </div>
        </div>


      </div>
    </ng-template>

    <!-- <ng-template pTemplate="footer">
      <div class="flex gap-2 justify-content-end">
        <p-button
          label="Limpar"
          icon="pi pi-times"
          severity="secondary"
          [outlined]="true"
          size="small"
          (onClick)="limparFiltros()"
          [disabled]="!hasActiveFilters()">
        </p-button>
      </div>
    </ng-template> -->
  </p-card>
</div>
