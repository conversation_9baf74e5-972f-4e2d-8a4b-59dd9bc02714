<div class="layout-principal">
  <!-- Menu Lateral -->
  <app-menu-lateral class="menu-lateral-container"></app-menu-lateral>

  <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
  <div class="conteudo-principal">
    
    <!-- Header com controles -->
    <div class="header-controles">
      <div class="flex align-items-center justify-content-between p-3 bg-white border-bottom-1 surface-border">
        <div class="flex align-items-center gap-3">
          <div class="header-title-section">
            <h4 class="m-0 text-primary">
              <i class="pi pi-map mr-2"></i>
              Mapa de Equipamentos
            </h4>

            @if (equipamentos().length > 0) {
              <span class="text-sm text-muted">
                {{ equipamentos().length }} equipamento(s) encontrado(s)
              </span>
            }

            <!-- Informações do usuário e filial -->
            <app-info-usuario-filial></app-info-usuario-filial>
          </div>
        </div>

        <div class="flex align-items-center gap-2">

          <!-- Bo<PERSON><PERSON> para recarregar -->
          <p-button
            icon="pi pi-refresh"
            [outlined]="true"
            size="small"
            pTooltip="Recarregar dados"
            tooltipPosition="bottom"
            [loading]="loading()"
            (onClick)="reloadData()">
          </p-button>

          <!-- Botão para toggle do painel de filtros -->
          <p-button
            [icon]="filtrosVisible() ? 'pi pi-times' : 'pi pi-filter'"
            [label]="isMobile() ? '' : 'Filtros'"
            size="small"
            [severity]="filtrosVisible() ? 'secondary' : 'primary'"
            (onClick)="toggleFiltros()">
          </p-button>
        </div>
      </div>
    </div>

    <!-- Área de conteúdo com mapa e filtros -->
    <div class="area-conteudo">
      
      <!-- Área do mapa -->
      <div class="mapa-container">
        
        <!-- Loading overlay -->
        @if (loading()) {
          <div class="loading-overlay">
            <div class="loading-content">
              <p-progressSpinner 
                [style]="{ width: '50px', height: '50px' }"
                strokeWidth="4">
              </p-progressSpinner>
              <p class="mt-3 text-center">Carregando equipamentos...</p>
            </div>
          </div>
        }

        <!-- Error message -->
        @if (error()) {
          <div class="error-overlay">
            <p-message 
              severity="error" 
              [text]="error()!"
              [closable]="true"
              (onClose)="error.set(null)">
            </p-message>
          </div>
        }

        <!-- Componente do mapa -->
        <app-mapa-google
          [equipamentos]="equipamentos()"
          class="w-full h-full"
          #mapaGoogle>
        </app-mapa-google>

        <!-- Info panel (quando não há equipamentos) -->
        @if (!loading() && equipamentos().length === 0 && !error()) {
          <div class="no-data-overlay">
            <div class="no-data-content text-center">
              <i class="pi pi-info-circle text-6xl text-muted mb-3"></i>
              <h5 class="text-muted">Nenhum equipamento encontrado</h5>
              <p class="text-sm text-muted">
                Ajuste os filtros para encontrar equipamentos ou verifique se há dados disponíveis.
              </p>
              <p-button
                label="Limpar Filtros"
                icon="pi pi-filter-slash"
                [outlined]="true"
                size="small"
                (onClick)="clearFilters()">
              </p-button>
            </div>
          </div>
        }
      </div>

      <!-- Painel de filtros (desktop) -->
      @if (!isMobile()) {
        <div class="filtros-panel" [class.visible]="filtrosVisible()">
          <app-painel-filtros></app-painel-filtros>
        </div>
      }
    </div>
  </div>

  <!-- Sidebar para filtros (mobile) -->
  @if (isMobile()) {
    <p-sidebar
      [(visible)]="filtrosVisible"
      position="right"
      [modal]="true"
      [dismissible]="true"
      styleClass="filtros-sidebar">
      
      <ng-template pTemplate="header">
        <div class="flex align-items-center gap-2">
          <i class="pi pi-filter"></i>
          <span class="font-semibold">Filtros</span>
        </div>
      </ng-template>

      <app-painel-filtros></app-painel-filtros>
    </p-sidebar>
  }

  <!-- Rodapé -->
  <!-- <app-rodape></app-rodape> -->
</div>
