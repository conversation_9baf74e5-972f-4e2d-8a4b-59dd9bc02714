import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { MapaFiltros, FILTROS_INICIAIS } from '../models/filtros.model';
import { EquipamentoFiltro } from '../models/equipamento.model';

@Injectable({
  providedIn: 'root'
})
export class MapaFiltrosService {
  private filtrosSubject = new BehaviorSubject<MapaFiltros>(FILTROS_INICIAIS);
  public filtros$ = this.filtrosSubject.asObservable();

  constructor() {}

  /**
   * Obtém os filtros atuais
   */
  getFiltros(): MapaFiltros {
    return this.filtrosSubject.value;
  }

  /**
   * Atualiza os filtros
   */
  updateFiltros(filtros: Partial<MapaFiltros>): void {
    const filtrosAtuais = this.getFiltros();
    const novosFiltros = { ...filtrosAtuais, ...filtros };
    this.filtrosSubject.next(novosFiltros);
  }

  /**
   * Reseta todos os filtros para o estado inicial
   */
  resetFiltros(): void {
    this.filtrosSubject.next(FILTROS_INICIAIS);
  }

  /**
   * Converte os filtros do mapa para o formato esperado pela API
   */
  toEquipamentoFiltro(filtros: MapaFiltros): EquipamentoFiltro {
    return {
      codFil: filtros.filial,
      tipoEquip: filtros.tiposEquipamento.length > 0 ? filtros.tiposEquipamento : undefined,
      nred: filtros.servicos.length > 0 ? filtros.servicos : undefined,
      bairro: filtros.bairros.length > 0 ? filtros.bairros : undefined,
      nredFat: filtros.clientes.length > 0 ? filtros.clientes : undefined,
      solicitante: filtros.solicitantes.length > 0 ? filtros.solicitantes : undefined
    };
  }

  /**
   * Verifica se há filtros aplicados
   */
  hasActiveFilters(): boolean {
    const filtros = this.getFiltros();
    return (
      filtros.tiposEquipamento.length > 0 ||
      filtros.servicos.length > 0 ||
      filtros.clientes.length > 0 ||
      filtros.bairros.length > 0 ||
      filtros.solicitantes.length > 0 ||
      !!filtros.empresa ||
      !!filtros.usuario ||
      !!filtros.filial ||
      !!filtros.dataInicio ||
      !!filtros.dataFim
    );
  }
}
