<div class="menu-lateral" [class.collapsed]="collapsed()">
  <!-- Header do Menu -->
  <div class="menu-header" (click)="toggleCollapsed()">
    <div class="logo-container" [class.collapsed]="collapsed()">
      @if (!collapsed()) {
        <div class="logo-content">
          <i class="pi pi-map text-primary text-2xl"></i>
          <span class="logo-text">Portal Caçambas</span>
        </div>
      } @else {
        <i class="pi pi-map text-primary text-2xl"></i>
      }
    </div>
    <i [class]="collapsed() ? 'pi pi-angle-right icon-arrow' : 'pi pi-angle-left icon-arrow'"></i>
  </div>

  <!-- Navegação Principal -->
  <nav class="menu-nav">

    <!-- Seção Operações -->
    <div class="menu-section">
      @if (!collapsed()) {
        <div class="section-header">
          <i class="pi pi-cog section-icon"></i>
          <span class="section-title">Operações</span>
        </div>
      } @else {
        <div class="section-divider"></div>
      }

      <!-- Containers -->
      <div class="menu-item-container">
        <p-button
          label="Containers"
          [text]="true"
          [class]="'menu-item ' + (isActive('/containers') ? 'active' : '')"
          [style]="{ 'justify-content': collapsed() ? 'center' : 'flex-start' }"
          routerLink="/containers"
          pRipple>
          <i class="pi pi-box" pButtonIcon></i>
          @if (!collapsed()) {
            <span class="menu-label">Containers</span>
          }
        </p-button>
      </div>

      <!-- Pedidos -->
      <div class="menu-item-container">
        <p-button
          label="Pedidos"
          [text]="true"
          [class]="'menu-item ' + (isActive('/pedidos') ? 'active' : '')"
          [style]="{ 'justify-content': collapsed() ? 'center' : 'flex-start' }"
          [disabled]="true"
          pRipple>
          <i class="pi pi-file-edit" pButtonIcon></i>
          @if (!collapsed()) {
            <span class="menu-label">Pedidos</span>
          }
        </p-button>
      </div>
    </div>

    <!-- Seção Clientes -->
    <div class="menu-section">
      @if (!collapsed()) {
        <div class="section-header">
          <i class="pi pi-users section-icon"></i>
          <span class="section-title">Clientes</span>
        </div>
      } @else {
        <div class="section-divider"></div>
      }

      <!-- Clientes -->
      <div class="menu-item-container">
        <p-button
          label="Clientes"
          [text]="true"
          [class]="'menu-item ' + (isActive('/clientes') ? 'active' : '')"
          [style]="{ 'justify-content': collapsed() ? 'center' : 'flex-start' }"
          [disabled]="true"
          pRipple>
          <i class="pi pi-users" pButtonIcon></i>
          @if (!collapsed()) {
            <span class="menu-label">Clientes</span>
          }
        </p-button>
      </div>
    </div>
  </nav>

  <!-- Footer do Menu -->
  <div class="menu-footer">
    @if (!collapsed()) {
      <div class="user-info">
        <p-avatar 
          icon="pi pi-user" 
          size="normal" 
          shape="circle"
          [style]="{ 'background-color': '#007bff', 'color': 'white' }">
        </p-avatar>
        <div class="user-details">
          <span class="user-name">Usuário</span>
          <span class="user-role">Administrador</span>
        </div>
      </div>
    } @else {
      <p-avatar 
        icon="pi pi-user" 
        size="normal" 
        shape="circle"
        [style]="{ 'background-color': '#007bff', 'color': 'white' }">
      </p-avatar>
    }
  </div>
</div>
