package com.br.sasw.portalcacamba.controller;

import com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro;
import com.br.sasw.portalcacamba.entity.dto.request.FilialFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse;
import com.br.sasw.portalcacamba.entity.dto.response.FilialResponse;
import com.br.sasw.portalcacamba.service.EquipamentoService;
import com.br.sasw.portalcacamba.service.FilialService;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/filiais")
@RequiredArgsConstructor
public class FilialController {

    private final FilialService service;

    @GetMapping
    public List<FilialResponse> findAll(@ParameterObject FilialFiltro filters) {
        return service.findAll(filters);
    }


}
