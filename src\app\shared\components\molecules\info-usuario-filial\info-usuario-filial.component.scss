.info-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.50rem;
}

.info-content {
  display: flex;
  flex-direction: column;
  font-size: 0.875rem;
}

.filial-info,
.endereco-info,
.usuario-info {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 600;
  color: #495057;
  min-width: 70px;
}

.value {
  color: #212529;
  flex: 1;
}

.loading-info,
.error-info {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #6c757d;
}

.error-info {
  color: #dc3545;
}

.pi-spinner {
  font-size: 0.875rem;
}

// Responsividade
@media (max-width: 768px) {
  .info-content {
    font-size: 0.8rem;
  }
  
  .filial-info,
  .endereco-info,
  .usuario-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .label {
    min-width: auto;
  }
}
