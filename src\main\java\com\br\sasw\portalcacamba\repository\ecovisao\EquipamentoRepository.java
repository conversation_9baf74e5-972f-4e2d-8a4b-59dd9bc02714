package com.br.sasw.portalcacamba.repository.ecovisao;

import com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EquipamentoRepository {

    List<EquipamentoResponse> findAll(EquipamentoFiltro filtro);

    List<String> findAllPlaces(String place);

    List<String> findAllServices(String service);

    List<String> findAllClients(String client);

    List<String> findAllRequester(String requester);

}
