package com.br.sasw.portalcacamba.service;

import com.br.sasw.portalcacamba.entity.dto.request.FilialFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.FilialResponse;
import com.br.sasw.portalcacamba.repository.ecovisao.FilialRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class FilialService {

    private final FilialRepository repository;

    public List<FilialResponse> findAll(FilialFiltro filters) {

        log.info("Filtros - {}", filters);
        return repository.findAll(filters);
    }
}
