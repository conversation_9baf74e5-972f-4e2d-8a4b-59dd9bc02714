.menu-lateral {
  width: 280px;
  min-width: 280px;
  height: 100vh;
  background: white;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  &.collapsed {
    width: 70px;
    min-width: 70px;
  }
}

.menu-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;

  .logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease-in-out;

    &.collapsed {
      justify-content: center;
    }

    .logo-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .logo-text {
      font-size: 1.25rem;
      font-weight: 600;
      color: #007bff;
      white-space: nowrap;
    }

  }

  .icon-arrow {
      color: #6c757d !important;
    }

  .toggle-btn {
    ::ng-deep {
      .p-button {
        width: 32px;
        height: 32px;
        color: #6c757d;

        &:hover {
          background-color: #f8f9fa;
          color: #007bff;
        }
      }
    }
  }
}

.menu-nav {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  .menu-section {
    margin-bottom: 1.5rem;

    .section-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      margin-bottom: 0.5rem;

      .section-icon {
        color: #6c757d;
        font-size: 0.875rem;
      }

      .section-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .section-divider {
      height: 1px;
      background-color: #e9ecef;
      margin: 0.5rem 1rem 1rem 1rem;
    }
  }

  .menu-item-container {
    margin-bottom: 0.50rem;

    ::ng-deep {
      .menu-item {
        width: 100%;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        transition: all 0.2s ease-in-out;
        color: #495057;
        font-weight: 500;

        .p-button-label {
          display: none;
        }

        .menu-label {
          // margin-left: 0.75rem;
          font-size: 0.875rem;
        }

        &:hover {
          background-color: #f8f9fa;
          color: #007bff;
          // transform: translateX(2px);
        }

        &.active {
          background-color: #e7f3ff;
          color: #007bff;
          font-weight: 600;
          box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:hover {
            background-color: transparent;
            color: #495057;
            transform: none;
          }
        }

        .p-badge {
          font-size: 0.6rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }
  }
}

.menu-footer {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;

  .user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .user-details {
      display: flex;
      flex-direction: column;
      min-width: 0;

      .user-name {
        font-size: 0.875rem;
        font-weight: 600;
        color: #495057;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .user-role {
        font-size: 0.75rem;
        color: #6c757d;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// Customizações para PrimeNG
::ng-deep {
  .p-button {
    border: none;
    background: transparent;
    box-shadow: none;

    &:enabled:hover {
      background-color: transparent;
    }

    &:focus {
      box-shadow: none;
    }
  }

  .p-avatar {
    flex-shrink: 0;
  }

  .p-badge {
    border-radius: 12px;
  }
}

// Responsividade
@media (max-width: 768px) {
  .menu-lateral {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);

    &.mobile-open {
      transform: translateX(0);
    }

    &.collapsed {
      width: 280px;
      min-width: 280px;
    }
  }
}

// Animações
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.menu-label {
  animation: slideIn 0.3s ease-in-out;
}
