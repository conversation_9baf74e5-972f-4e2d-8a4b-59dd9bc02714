import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  Input,
  OnDestroy,
  OnInit,
  signal
} from '@angular/core';
import { Subject } from 'rxjs';

// Google Maps
import { GoogleMapsModule } from '@angular/google-maps';

// Models
import { EquipamentoResponse } from '@core/models/equipamento.model';

@Component({
  selector: 'app-mapa-google',
  standalone: true,
  imports: [
    CommonModule,
    GoogleMapsModule
  ],
  templateUrl: './mapa-google.component.html',
  styleUrl: './mapa-google.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapaGoogleComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject<void>();

  @Input() set equipamentos(value: EquipamentoResponse[]) {
    this._equipamentos.set(value || []);
  }

  private _equipamentos = signal<EquipamentoResponse[]>([]);

  get equipamentosSignal() {
    return this._equipamentos.asReadonly();
  }

  // Configurações do mapa
  center = signal<google.maps.LatLngLiteral>({ lat: -15.7942, lng: -47.8822 });
  zoom = signal<number>(10);

  // Opções do mapa
  options = signal<google.maps.MapOptions>({
    mapTypeId: 'roadmap',
    zoomControl: true,
    scrollwheel: true,
    disableDoubleClickZoom: false,
    maxZoom: 20,
    minZoom: 10,
    styles: [
      {
        featureType: 'poi',
        elementType: 'labels',
        stylers: [{ visibility: 'off' }]
      }
    ]
  });

  // Marcadores computados
  markers = computed(() => {
    return this._equipamentos().map(equipamento => {
      const lat = parseFloat(equipamento.latitude);
      const lng = parseFloat(equipamento.longitude);

      if (isNaN(lat) || isNaN(lng)) {
        return null;
      }

      const iconUrl = this.getIconForEquipmentType(equipamento);
      // const iconUrl = this.createMarkerSVG(equipamento.tempoDias.toString());
      // const svgUrl = 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(iconUrl);
      console.log(equipamento);
      return {
        position: { lat, lng },
        title: `${equipamento.nred} - ${equipamento.endereco}`,
        options: {
          icon: {
            url: iconUrl,
            scaledSize: new google.maps.Size(32, 32),
            // anchor: new google.maps.Point(16, 32),
          },
        },
        equipamento
      };
    }).filter(marker => marker !== null);
  });

  ngOnInit(): void {
    // Inicialização se necessária
  }

  ngAfterViewInit(): void {
    // Configurações após a view ser inicializada
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Mapeia o tipo de equipamento para o ícone correspondente
   */
  private getIconForEquipmentType(equipamento: EquipamentoResponse): string {
    const iconMap: { [key: string]: string } = {
      'CP': 'cp_',
      'CR': 'cr_',
      'LX': 'lx_',
      'TR': 'tr_',
      'CA': 'ca_',
      'CO': 'cp_'
    };

    const iconFileName = iconMap[equipamento.tipoEquipamento];
    return `assets/images/${iconFileName}` + this.getMarkerColor(equipamento);
  }

  /**
   * Determina a cor do marcador baseado na data de previsão de coleta
   */
  private getMarkerColor(equipamento: EquipamentoResponse): string {

    try {
      // Converte a data de previsão (formato DD/MM/YYYY)
      const [dia, mes, ano] = equipamento.dataPrevisaoColeta.split('/');
      const dataPrevisao = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

      // Adiciona os dias limite
      const dataLimite = new Date(dataPrevisao);
      dataLimite.setDate(dataLimite.getDate() + equipamento.limite);

      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0); // Remove horas para comparação apenas de datas
      dataLimite.setHours(0, 0, 0, 0);

      if (dataLimite > hoje) {
        return 'verde.png'; // Verde - dentro do prazo
      } else if (dataLimite.getTime() === hoje.getTime()) {
        return 'amarelo.png'; // Amarelo - vence hoje
      } else {
        return 'vermelho.png'; // Vermelho - vencido
      }
    } catch (error) {
      console.warn('Erro ao calcular cor do marcador:', error);
      return '#6c757d'; // Cinza para erro
    }
  }

  /**
   * Manipula o clique no marcador
   */
  onMarkerClick(marker: any): void {
    const equipamento = marker.equipamento;

    // Cria o conteúdo do InfoWindow
    const infoContent = this.createInfoWindowContent(equipamento);

    // Aqui você pode implementar a lógica para mostrar o InfoWindow
    // Por exemplo, usando um serviço de modal ou tooltip
    console.log('Marcador clicado:', equipamento);
  }

  /**
   * Cria o conteúdo do InfoWindow
   */
  private createInfoWindowContent(equipamento: EquipamentoResponse): string {

    return `
      <div class="info-window">
        <h6><strong>${equipamento.nred}</strong></h6>
        <p><strong>Tipo:</strong> ${equipamento.tipoEquipamento}</p>
        <p><strong>Endereço:</strong> ${equipamento.endereco}</p>
        <p><strong>Bairro:</strong> ${equipamento.bairro}</p>
        <p><strong>Cliente:</strong> ${equipamento.nome}</p>
        <p><strong>Situação:</strong> ${equipamento.situacao === 'A' ? 'Ativo' : 'Inativo'}</p>
        <p><strong>Última Movimentação:</strong> ${equipamento.dataUltMovimento}</p>
        <p><strong>Previsão de Coleta:</strong> ${equipamento.dataPrevisaoColeta}</p>
        <p><strong>Tempo (dias):</strong> ${equipamento.tempoDias}</p>
      </div>
    `;
  }

  /**
   * Centraliza o mapa em uma posição específica
   */
  centerMap(lat: number, lng: number, zoom?: number): void {
    this.center.set({ lat, lng });
    if (zoom) {
      this.zoom.set(zoom);
    }
  }

  /**
   * Ajusta o zoom para mostrar todos os equipamentos
   */
  fitBounds(): void {
    const markers = this.markers();
    if (markers.length === 0) return;

    const bounds = new google.maps.LatLngBounds();
    markers.forEach(marker => {
      if (marker) {
        bounds.extend(marker.position);
      }
    });

    // Aqui você precisaria acessar a instância do mapa para usar fitBounds
    // Isso pode ser feito através de ViewChild ou eventos do GoogleMapsModule
  }

  /**
   * Atualiza os marcadores (chamado quando os equipamentos mudam)
   */
  updateMarkers(): void {
    // Os marcadores são atualizados automaticamente através do computed signal
    // Este método pode ser usado para lógica adicional se necessário
  }

  private createMarkerSVG(texto: string = ''): string {
    return `<svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="2048" height="2048" viewBox="0 0 800 1024" preserveAspectRatio="xMidYMid meet">
<g transform="translate(0.000000,1024.000000) scale(0.100000,-0.100000)" fill="#0d773dff" stroke="none">
<path d="M2970 8844 c-102 -24 -513 -118 -915 -210 -1006 -229 -962 -218 -991 -245 -21 -20 -199 -299 -366 -573 -46 -75 -48 -83 -48 -150 1 -63 5 -79 38 -139 l37 -69 91 -40 91 -39 174 -377 c269 -587 293 -636 321 -654 14 -9 119 -61 234 -116 115 -55 391 -189 614 -298 l405 -199 90 0 c82 1 99 4 190 41 357 146 943 380 1068 427 166 62 202 81 228 117 12 15 32 91 53 195 66 326 311 1439 321 1455 3 4 23 11 45 15 22 4 51 16 65 27 47 37 55 67 55 203 0 145 -11 182 -63 209 -18 9 -189 64 -382 122 -192 58 -496 151 -675 206 -400 124 -449 138 -475 137 -11 -1 -103 -21 -205 -45z m192 -126 c-7 -7 -36 -18 -65 -24 -82 -18 -338 -78 -632 -149 -437 -105 -1012 -241 -1121 -265 l-102 -23 -63 -121 c-34 -66 -96 -181 -136 -255 -40 -74 -72 -135 -71 -136 2 -1 64 -28 138 -58 74 -31 375 -160 668 -285 l533 -229 17 25 c9 14 94 152 188 306 153 251 174 282 205 293 19 6 151 42 294 78 289 74 988 261 1220 325 396 111 397 111 404 104 15 -15 -48 -41 -244 -98 -240 -71 -840 -236 -1275 -351 -151 -41 -287 -78 -301 -84 -20 -7 -57 -60 -146 -208 -301 -499 -304 -503 -329 -509 -60 -15 -88 -4 -1074 422 -234 102 -440 191 -457 199 l-33 15 44 62 c24 35 93 144 153 243 182 299 172 285 198 285 13 0 81 13 152 29 1234 283 1787 411 1792 417 2 2 16 4 30 4 20 0 22 -3 13 -12z m906 -646 c-11 -13 -10 -15 4 -9 10 4 17 2 15 -4 -1 -6 6 -16 16 -22 14 -10 20 -9 27 3 6 9 10 10 10 3 0 -17 -29 -31 -51 -26 -14 4 -19 0 -19 -16 0 -24 -15 -28 -27 -7 -6 12 -11 9 -20 -10 -10 -22 -9 -24 5 -20 9 3 30 8 45 12 22 5 26 9 15 16 -9 7 -2 8 20 4 20 -3 31 -2 27 4 -3 5 1 7 9 4 8 -3 53 4 101 16 47 13 88 20 91 17 3 -2 -19 -10 -48 -17 -57 -14 -324 -88 -813 -225 -170 -48 -380 -106 -465 -130 -121 -34 -159 -48 -176 -68 -12 -14 -99 -153 -193 -310 l-171 -285 -76 -52 c-41 -29 -77 -50 -79 -48 -3 2 14 16 38 31 23 15 56 40 72 55 17 15 26 24 19 19 -6 -4 -20 -5 -30 -1 -14 4 -15 3 -5 -4 18 -13 -24 -42 -62 -42 -15 0 -26 -4 -23 -8 2 -4 1 -14 -4 -21 -5 -9 -10 1 -15 30 -6 37 -4 45 15 61 12 10 31 18 41 18 10 0 19 7 19 15 0 8 3 15 8 15 4 0 16 18 26 41 11 22 24 43 29 46 6 3 7 1 3 -5 -4 -7 -1 -12 9 -12 9 0 13 5 10 10 -3 6 -1 10 5 10 6 0 8 5 5 10 -4 6 -11 8 -16 5 -5 -3 -6 1 -3 9 3 9 10 16 14 16 4 0 11 12 14 26 l7 26 24 -28 c27 -31 31 -44 15 -44 -5 0 -10 8 -10 18 0 14 -2 15 -9 2 -6 -8 -8 -17 -5 -21 2 -4 0 -10 -6 -14 -21 -13 -9 -24 16 -15 19 8 24 7 19 -3 -9 -17 25 25 36 46 10 17 1 23 -26 17 -5 -1 -5 4 2 11 15 15 -4 49 -24 42 -7 -3 -13 0 -13 7 0 6 4 9 8 6 4 -2 14 -1 22 5 12 7 12 9 -3 9 -10 0 -15 5 -12 10 4 6 10 8 15 5 5 -3 12 0 16 6 4 8 3 9 -4 5 -7 -4 -12 -2 -12 3 0 6 5 11 10 11 6 0 10 6 10 13 1 6 25 50 55 97 30 47 62 100 72 118 9 17 22 32 30 32 7 0 10 6 7 14 -3 7 5 30 17 50 16 27 24 34 34 26 9 -8 14 -6 18 4 4 9 1 18 -5 22 -7 5 -8 2 -3 -6 5 -9 4 -11 -3 -6 -13 8 -1 40 21 52 8 5 7 2 -2 -9 -12 -15 -11 -17 3 -17 9 0 16 8 16 19 0 11 3 22 8 25 8 4 244 68 342 91 30 7 104 27 164 44 60 18 116 29 125 26 8 -3 12 -3 8 2 -8 7 23 16 273 84 171 46 190 50 202 43 6 -4 8 -3 5 3 -4 6 5 13 21 16 15 3 34 8 43 12 10 4 16 1 16 -8 0 -9 -9 -13 -23 -11 -13 1 -28 -4 -35 -12 -8 -10 -8 -14 0 -14 6 0 11 4 11 9 0 5 8 8 18 6 9 -2 20 -4 25 -4 4 -1 7 -7 7 -15 0 -9 7 -12 20 -9 11 3 17 9 14 14 -3 5 -10 6 -16 3 -6 -4 -6 2 1 15 7 11 9 21 6 21 -4 0 -3 5 0 10 4 6 11 8 16 4 6 -3 5 -12 -3 -22z m339 -339 c-30 -126 -84 -367 -133 -587 -27 -120 -50 -214 -52 -208 -2 6 -22 -10 -45 -35 -43 -46 -189 -167 -267 -220 -224 -152 -1109 -607 -1142 -587 -6 4 -8 3 -5 -3 10 -15 -30 -23 -42 -9 -6 7 -27 55 -47 107 -19 52 -74 190 -121 307 -46 118 -82 221 -79 229 4 8 1 11 -5 7 -6 -3 -8 -16 -5 -29 13 -53 316 -809 326 -815 16 -10 -12 -20 -53 -20 -29 0 -292 118 -283 127 2 2 40 -14 85 -36 44 -23 83 -41 85 -41 3 0 -11 37 -30 83 -20 45 -77 182 -127 304 -50 122 -93 221 -95 219 -3 -4 92 -251 108 -278 6 -10 10 -19 10 -22 0 -2 -54 23 -120 56 -73 36 -118 64 -114 70 4 7 2 8 -4 4 -15 -9 -85 21 -77 34 3 5 0 7 -8 4 -15 -6 -385 180 -647 325 -36 20 -82 45 -102 56 -25 13 -33 22 -25 27 7 4 9 8 5 8 -3 0 -14 3 -24 7 -12 4 -15 3 -10 -5 4 -7 5 -12 2 -12 -3 0 -12 12 -20 28 -18 31 -21 16 -5 -25 28 -76 -19 12 -105 195 -82 175 -92 202 -73 197 12 -4 103 -44 202 -90 99 -46 221 -101 270 -123 50 -22 128 -58 175 -80 333 -155 528 -242 541 -242 8 0 28 14 45 31 16 17 58 51 94 75 96 64 126 100 245 294 60 96 133 214 162 262 30 48 62 89 71 92 9 3 139 37 287 75 399 103 667 176 827 225 218 67 284 86 299 85 8 0 -8 -9 -36 -20 -27 -10 -38 -17 -23 -14 14 3 34 8 44 12 12 4 15 3 10 -5 -4 -7 -2 -12 4 -12 6 0 8 -5 5 -10 -4 -6 -9 -27 -11 -47 -3 -20 -9 -42 -14 -48 -5 -6 -5 -16 2 -25 8 -12 9 -12 4 2 -3 10 -1 20 3 23 5 2 12 22 16 42 9 56 28 114 33 108 3 -2 -2 -35 -11 -72z"/></g></svg>`;
    //       ${imagem}
    //       <text x="20" y="35" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="$ff4827">
    //         ${texto}
    //       </text>
    //     </svg>
    //   `;
    //   }
    // }
  }
}
