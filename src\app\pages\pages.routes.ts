import type { Routes } from '@angular/router';
import { PagesComponent } from './pages.component';

export const PAGES_ROUTES: Routes = [
  {
    path: '',
    component: PagesComponent,
    children: [
      {
        path: '',
        redirectTo: '/containers',
        pathMatch: 'full'
      },
      {
        path: 'home',
        loadComponent: () =>
          import('./home/<USER>').then((c) => c.HomeComponent),
      },
      {
        path: 'containers',
        loadComponent: () =>
          import('./mapa/mapa.component').then((c) => c.MapaComponent),
      },
      {
        path: 'mapa',
        redirectTo: '/containers',
        pathMatch: 'full'
      },
    ],
  },
];
